import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import authService from "../services/auth-service";
import userService from "../services/user-service";
import { TOKEN_STORAGE } from "../configurations/api-config";
import { UserResponse } from "../interfaces/models/auth";

interface AuthContextType {
    isLogin: boolean;
    isAdmin: boolean;
    user: UserResponse | null;
    userProfile: UserResponse | null;
    userPermissions: string[];
    login: (authResponse: { token: string, refreshToken: string, expiresIn?: number }) => void;
    logout: () => void;
    refreshUserProfile: () => Promise<void>;
    hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
    const [isLogin, setIsLogin] = useState<boolean>(() => {
        return !!localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
    });
    const [isAdmin, setIsAdmin] = useState<boolean>(false);
    const [userPermissions, setUserPermissions] = useState<string[]>([]);
    const [user, setUser] = useState<UserResponse | null>(null);
    const [userProfile, setUserProfile] = useState<UserResponse | null>(null);

    // Hàm kiểm tra quyền cụ thể
    const hasPermission = (permission: string): boolean => {
        return userPermissions.includes(permission) || isAdmin;
    };

    // Lấy thông tin người dùng khi đã đăng nhập
    useEffect(() => {
        const fetchUserInfo = async () => {
            if (isLogin) {
                // Lấy thông tin cơ bản của người dùng từ token JWT
                const userInfo = authService.getMyInfo();
                if (userInfo) {
                    console.log("AuthContext: Thông tin người dùng từ token:", userInfo);
                    setUser(userInfo);

                    // Kiểm tra quyền từ token
                    const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
                    if (token) {
                        try {
                            const base64Url = token.split('.')[1];
                            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                            }).join(''));

                            const payload = JSON.parse(jsonPayload);

                            // Lấy danh sách quyền từ token
                            const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];
                            setUserPermissions(permissions);

                            // Kiểm tra các quyền cụ thể
                            const hasAdminRole = permissions.includes('ROLE_ADMIN');

                            console.log("AuthContext: Quyền của người dùng:", permissions);
                            console.log("AuthContext: Có quyền ADMIN:", hasAdminRole);
                            console.log("AuthContext: Có quyền MANGA_MANAGEMENT:", permissions.includes('MANGA_MANAGEMENT'));
                            console.log("AuthContext: Có quyền SYSTEM_MANAGEMENT:", permissions.includes('SYSTEM_MANAGEMENT'));

                            setIsAdmin(hasAdminRole);
                        } catch (error) {
                            console.error("AuthContext: Lỗi khi parse token:", error);
                        }
                    }

                    // Lấy thông tin profile chi tiết
                    try {
                        const myProfile = await userService.getMyProfile();
                        if (myProfile) {
                            console.log("AuthContext: Profile người dùng:", myProfile);
                            setUserProfile(myProfile);
                        }
                    } catch (error) {
                        console.error("AuthContext: Lỗi khi lấy profile người dùng:", error);
                    }
                }
            } else {
                setUser(null);
                setUserProfile(null);
                setIsAdmin(false);
                setUserPermissions([]);
            }
        };

        fetchUserInfo();
    }, [isLogin]);

    const login = (authResponse: { token: string; refreshToken: string; expiresIn?: number }) => {
        localStorage.setItem(TOKEN_STORAGE.ACCESS_TOKEN, authResponse.token);
        localStorage.setItem(TOKEN_STORAGE.REFRESH_TOKEN, authResponse.refreshToken);

        if (authResponse.expiresIn) {
            const expiresAt = new Date();
            expiresAt.setSeconds(expiresAt.getSeconds() + authResponse.expiresIn);
            localStorage.setItem(TOKEN_STORAGE.TOKEN_EXPIRY, expiresAt.toISOString());
        }

        setIsLogin(true);
    };

    const logout = () => {
        localStorage.removeItem(TOKEN_STORAGE.ACCESS_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.REFRESH_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.TOKEN_EXPIRY);
        setIsLogin(false);
        setUser(null);
        setUserProfile(null);
        setIsAdmin(false);
        setUserPermissions([]);
    };

    const refreshUserProfile = async () => {
        if (isLogin) {
            try {
                const myProfile = await userService.getMyProfile();
                if (myProfile) {
                    console.log("AuthContext: Profile người dùng (refresh):", myProfile);
                    setUserProfile(myProfile);
                }
                return;
            } catch (error) {
                console.error("AuthContext: Lỗi khi refresh profile người dùng:", error);
            }
        }
    };

    return (
        <AuthContext.Provider
            value={{
                isLogin,
                isAdmin,
                user,
                userProfile,
                userPermissions,
                login,
                logout,
                refreshUserProfile,
                hasPermission
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
