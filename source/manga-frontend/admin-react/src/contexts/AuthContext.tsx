import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import authService from "../services/auth-service";
import userService from "../services/user-service";
import { TOKEN_STORAGE } from "../configurations/api-config";
import { UserResponse } from "../interfaces/models/auth";

interface AuthContextType {
    isLogin: boolean;
    isAdmin: boolean;
    hasMangaManagement: boolean;
    hasSystemManagement: boolean;
    userPermissions: string[];
    user: UserResponse | null;
    userProfile: UserResponse | null;
    login: (authResponse: { token: string, refreshToken: string, expiresIn?: number }) => void;
    logout: () => void;
    refreshUserProfile: () => Promise<void>;
    hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
    const [isLogin, setIsLogin] = useState<boolean>(() => {
        return !!localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
    });
    const [isAdmin, setIsAdmin] = useState<boolean>(false);
    const [hasMangaManagement, setHasMangaManagement] = useState<boolean>(false);
    const [hasSystemManagement, setHasSystemManagement] = useState<boolean>(false);
    const [userPermissions, setUserPermissions] = useState<string[]>([]);
    const [user, setUser] = useState<UserResponse | null>(null);
    const [userProfile, setUserProfile] = useState<UserResponse | null>(null);

    // Hàm kiểm tra quyền cụ thể
    const hasPermission = (permission: string): boolean => {
        return userPermissions.includes(permission);
    };

    // Lấy thông tin người dùng khi đã đăng nhập
    useEffect(() => {
        const fetchUserInfo = async () => {
            if (isLogin) {
                // Lấy thông tin cơ bản của người dùng từ token JWT
                const userInfo = authService.getMyInfo();
                if (userInfo) {
                    console.log("AuthContext: Thông tin người dùng từ token:", userInfo);
                    setUser(userInfo);

                    // Kiểm tra quyền từ token
                    const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
                    if (token) {
                        try {
                            const base64Url = token.split('.')[1];
                            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                            }).join(''));

                            const payload = JSON.parse(jsonPayload);

                            // Lấy danh sách quyền từ token
                            const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];
                            setUserPermissions(permissions);

                            // Kiểm tra các quyền cụ thể
                            const hasAdminRole = permissions.includes('ROLE_ADMIN');
                            const hasMangaManagementPerm = permissions.includes('MANGA_MANAGEMENT');
                            const hasSystemManagementPerm = permissions.includes('SYSTEM_MANAGEMENT');

                            console.log("AuthContext: Có quyền ADMIN:", hasAdminRole);
            console.log("AuthContext: Thông tin người dùng từ token:", userInfo);
            setUser(userInfo);

            // Kiểm tra quyền admin từ token
            try {
                const base64Url = authResponse.token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                const payload = JSON.parse(jsonPayload);
                const hasAdminRole = payload.scope && payload.scope.includes('ROLE_ADMIN');
                console.log("AuthContext: Có quyền ADMIN:", hasAdminRole);
                setIsAdmin(hasAdminRole);
            } catch (error) {
                console.error("Lỗi khi kiểm tra quyền admin:", error);
                setIsAdmin(false);
            }

            // Lấy thông tin chi tiết từ API
            if (userInfo.id) {
                try {
                    const profileData = await userService.getProfileByUserId(userInfo.id);
                    if (profileData) {
                        console.log("AuthContext: Thông tin chi tiết người dùng từ API:", profileData);
                        setUserProfile(profileData);
                    }
                } catch (error) {
                    console.error("Lỗi khi lấy thông tin chi tiết người dùng:", error);
                }
            }
        }
    };

    const logout = () => {
        // Xóa tất cả các token khỏi localStorage
        localStorage.removeItem(TOKEN_STORAGE.ACCESS_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.REFRESH_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.TOKEN_EXPIRY);

        setIsLogin(false);
        setIsAdmin(false);
        setUser(null);
        setUserProfile(null);
    };

    return (
        <AuthContext.Provider value={{ isLogin, isAdmin, user, userProfile, login, logout, refreshUserProfile }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};

