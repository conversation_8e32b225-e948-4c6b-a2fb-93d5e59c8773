package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.request.MangaRequest;
import com.raindrop.manga_service.dto.response.MangaManagementResponse;
import com.raindrop.manga_service.dto.response.MangaResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.entity.Manga;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-26T02:22:12+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Oracle Corporation)"
)
@Component
public class MangaMapperImpl implements MangaMapper {

    @Override
    public Manga toManga(MangaRequest request) {
        if ( request == null ) {
            return null;
        }

        Manga.MangaBuilder manga = Manga.builder();

        manga.yearOfRelease( request.getYearOfRelease() );
        manga.status( request.getStatus() );
        manga.title( request.getTitle() );
        manga.author( request.getAuthor() );
        manga.description( request.getDescription() );

        manga.deleted( false );

        return manga.build();
    }

    @Override
    public MangaResponse toMangaResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaResponse.MangaResponseBuilder mangaResponse = MangaResponse.builder();

        mangaResponse.genres( genresToStringList( manga.getGenres() ) );
        mangaResponse.lastChapterId( manga.getLastChapterId() );
        mangaResponse.id( manga.getId() );
        mangaResponse.title( manga.getTitle() );
        mangaResponse.author( manga.getAuthor() );
        mangaResponse.loves( manga.getLoves() );
        mangaResponse.views( manga.getViews() );
        mangaResponse.comments( manga.getComments() );
        mangaResponse.coverUrl( manga.getCoverUrl() );
        mangaResponse.description( manga.getDescription() );
        mangaResponse.yearOfRelease( manga.getYearOfRelease() );
        mangaResponse.status( manga.getStatus() );
        mangaResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaResponse.createdAt( manga.getCreatedAt() );
        mangaResponse.updatedAt( manga.getUpdatedAt() );
        mangaResponse.deleted( manga.isDeleted() );
        mangaResponse.deletedAt( manga.getDeletedAt() );
        mangaResponse.deletedBy( manga.getDeletedBy() );

        return mangaResponse.build();
    }

    @Override
    public MangaSummaryResponse toMangaSummaryResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaSummaryResponse.MangaSummaryResponseBuilder mangaSummaryResponse = MangaSummaryResponse.builder();

        mangaSummaryResponse.id( manga.getId() );
        mangaSummaryResponse.title( manga.getTitle() );
        mangaSummaryResponse.author( manga.getAuthor() );
        mangaSummaryResponse.coverUrl( manga.getCoverUrl() );
        mangaSummaryResponse.lastChapterId( manga.getLastChapterId() );
        mangaSummaryResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaSummaryResponse.views( manga.getViews() );
        mangaSummaryResponse.loves( manga.getLoves() );
        mangaSummaryResponse.comments( manga.getComments() );
        mangaSummaryResponse.status( manga.getStatus() );
        mangaSummaryResponse.createdAt( manga.getCreatedAt() );
        mangaSummaryResponse.updatedAt( manga.getUpdatedAt() );

        return mangaSummaryResponse.build();
    }

    @Override
    public void updateManga(Manga manga, MangaRequest request) {
        if ( request == null ) {
            return;
        }

        manga.setTitle( request.getTitle() );
        manga.setAuthor( request.getAuthor() );
        manga.setDescription( request.getDescription() );
        manga.setYearOfRelease( request.getYearOfRelease() );
        manga.setStatus( request.getStatus() );
    }

    @Override
    public MangaManagementResponse toMangaManagementResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaManagementResponse.MangaManagementResponseBuilder mangaManagementResponse = MangaManagementResponse.builder();

        mangaManagementResponse.genres( genresToStringList( manga.getGenres() ) );
        mangaManagementResponse.id( manga.getId() );
        mangaManagementResponse.title( manga.getTitle() );
        mangaManagementResponse.author( manga.getAuthor() );
        mangaManagementResponse.loves( manga.getLoves() );
        mangaManagementResponse.views( manga.getViews() );
        mangaManagementResponse.comments( manga.getComments() );
        mangaManagementResponse.coverUrl( manga.getCoverUrl() );
        mangaManagementResponse.description( manga.getDescription() );
        mangaManagementResponse.yearOfRelease( manga.getYearOfRelease() );
        mangaManagementResponse.status( manga.getStatus() );
        mangaManagementResponse.lastChapterId( manga.getLastChapterId() );
        mangaManagementResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaManagementResponse.createdAt( manga.getCreatedAt() );
        mangaManagementResponse.updatedAt( manga.getUpdatedAt() );
        mangaManagementResponse.deleted( manga.isDeleted() );
        mangaManagementResponse.deletedAt( manga.getDeletedAt() );
        mangaManagementResponse.deletedBy( manga.getDeletedBy() );

        return mangaManagementResponse.build();
    }
}
