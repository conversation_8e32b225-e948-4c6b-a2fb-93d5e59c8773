server:
  port: 8082
  servlet:
    context-path: /manga
    multipart:
      max-file-size: 50MB
      max-request-size: 1000MB
spring:
  datasource:
    url: "*****************************************"
    username: "root"
    password: ""
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  data:
    web:
      pageable:
        default-page-size: 10
        max-page-size: 100
        page-parameter: page
        size-parameter: size
        one-indexed-parameters: false
  kafka:
    bootstrap-servers: localhost:9094
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: manga-service
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
app:
  services:
    upload: http://localhost:8083/upload
    history: http://localhost:8087/history

feign:
  httpclient:
    enabled: true
    max-connections: 200
    max-connections-per-route: 50
    disable-ssl-validation: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      upload-service:
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: full
        decode404: true

jwt:
  signerKey: "o6lSCt2tIkiqLnuj/m+P/My5Nq4w6C47rvMCAQIXJp8+I4lxliuh/EMEFM/YS9Aa"

logging:
  level:
    org.springframework.web: DEBUG
    org.hibernate.validator: DEBUG
    feign: DEBUG
    feign.Logger: DEBUG
    org.apache.http: DEBUG